"use client";

import { useState } from "react";

type PackageOption = '@apollo/ui' | '@design-systems/apollo-ui' | 'combined';

export default function Home() {
  const [selectedPackage, setSelectedPackage] = useState<PackageOption>('@apollo/ui');


  return (
    <div className="min-h-screen bg-gray-50 p-6">
        {/* Header with Selection */}
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Apollo UI Package Explorer
          </h1>
          <p className="text-gray-600 mb-6">
            Select a package option to see live examples and components in action.
          </p>
          {/* Package Selection Buttons */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => setSelectedPackage('@apollo/ui')}
              className={`px-6 py-4 rounded-lg font-semibold text-center transition-all duration-200 border-2 ${selectedPackage === '@apollo/ui'
                  ? 'bg-blue-600 text-white border-blue-600 shadow-lg transform scale-105'
                  : 'bg-white text-blue-600 border-blue-300 hover:bg-blue-50 hover:border-blue-400'
                }`}
            >
              <div className="text-lg font-bold">@apollo/ui</div>
              <div className="text-sm opacity-90 mt-1">Recommended</div>
            </button>
            <button
              onClick={() => setSelectedPackage('@design-systems/apollo-ui')}
              className={`px-6 py-4 rounded-lg font-semibold text-center transition-all duration-200 border-2 ${selectedPackage === '@design-systems/apollo-ui'
                  ? 'bg-green-600 text-white border-green-600 shadow-lg transform scale-105'
                  : 'bg-white text-green-600 border-green-300 hover:bg-green-50 hover:border-green-400'
                }`}
            >
              <div className="text-lg font-bold">@design-systems/apollo-ui</div>
            </button>
            <button
              onClick={() => setSelectedPackage('combined')}
              className={`px-6 py-4 rounded-lg font-semibold text-center transition-all duration-200 border-2 ${selectedPackage === 'combined'
                  ? 'bg-purple-600 text-white border-purple-600 shadow-lg transform scale-105'
                  : 'bg-white text-purple-600 border-purple-300 hover:bg-purple-50 hover:border-purple-400'
                }`}
            >
              <div className="text-lg font-bold">Combined</div>
            </button>
        </div>
    </div>
  );
}